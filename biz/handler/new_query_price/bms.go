package new_query_price

import (
	"slices"

	"work.ctyun.cn/git/openapi/openapi-base/openapi/starship_openapi"
	stmod "work.ctyun.cn/git/openapi/openapi-base/openapi/starship_openapi/model"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/constant"
)

type bms struct {
	ctx *queryNewOrderPriceContext
}

func (b *bms) buildItem() (items []map[string]any) {
	items = []map[string]any{
		b.buildBMSItem(),
	}
	if b.ctx.req.Bandwidth != nil && *b.ctx.req.Bandwidth != 0 {
		items = append(items, b.buildIpItem())
	}
	if len(b.ctx.req.Disks) > 0 {
		items = append(items, b.buildDiskItems()...)
	}
	return
}

func (b *bms) buildBMSItem() (result map[string]any) {
	// 初始化磁盘大小统计
	ssdSize, sasSize, sataSize := 0, 0, 0
	volumeSize := 0

	// 处理OrderDisks中的磁盘数据
	// TODO: python 中使用的是 disks, 还需要再确认下
	for _, disk := range b.ctx.req.OrderDisks {
		if disk.DiskType == nil || disk.DiskSize == nil {
			continue
		}
		volumeType := "SATA" // 默认类型
		if disk.DiskType != nil && *disk.DiskType != constant.NullStr {
			volumeType = *disk.DiskType
		}
		diskSize := *disk.DiskSize
		volumeSize += diskSize
		switch volumeType {
		case "SAS":
			sasSize += diskSize
		case "SSD":
			ssdSize += diskSize
		default:
			sataSize += diskSize
		}
	}

	// 处理系统盘
	systemVolumeSize := b.ctx.bmsSyshd
	syshdType := b.ctx.bmsSyshdType
	switch syshdType {
	case "SAS":
		sasSize += systemVolumeSize
	case "SSD":
		ssdSize += systemVolumeSize
	default:
		sataSize += systemVolumeSize
	}

	// 处理数据盘
	dataVolumeSize := b.ctx.bmsDatahd
	datahdType := b.ctx.bmsDatahdType
	switch datahdType {
	case "SAS":
		sasSize += dataVolumeSize
	case "SSD":
		ssdSize += dataVolumeSize
	default:
		sataSize += dataVolumeSize
	}

	// 处理NVME盘
	nvmeVolumeSize := b.ctx.bmsNvmehd
	nvmehdType := b.ctx.bmsNvmehdType
	switch nvmehdType {
	case "SAS":
		sasSize += nvmeVolumeSize
	case "SSD":
		ssdSize += nvmeVolumeSize
	default:
		sataSize += nvmeVolumeSize
	}

	// 构建BMS配置
	return map[string]any{
		"resourceType": "BMS",
		"serviceTag":   "OVMS",
		"master":       true,
		"itemConfig": map[string]any{
			"platformId":     b.ctx.req.RegionID,
			"regionId":       b.ctx.regionInfo.CtyunId,
			"cpuNum":         b.ctx.bmsCpuNum,
			"memSize":        b.ctx.bmsMemSize,
			"bmsType":        b.ctx.bmsType,
			"bmsProductType": b.ctx.bmsProductType,
			"bmsRDMAType":    b.ctx.bmsRDMAType,
			"SSD":            ssdSize,
			"SAS":            sasSize,
			"SATA":           sataSize,
		},
		"itemValue": 1,
	}
}

// buildSysDiskItem 构架数据盘item，数据盘可能会有多个item
func (b *bms) buildDiskItems() (result []map[string]any) {
	result = make([]map[string]any, 0, len(b.ctx.req.Disks))
	for _, disk := range b.ctx.req.Disks {
		result = append(result, map[string]any{
			"master":       false,
			"resourceType": "EBS",
			"serviceTag":   "OVMS",
			"itemConfig": map[string]any{
				"volumeType": disk.DiskType,
			},
			"itemValue": disk.DiskSize,
		})
	}
	return
}

// buildIpItem 弹性ip
func (b *bms) buildIpItem() map[string]any {
	return map[string]any{
		"master":       false,
		"resourceType": "NETWORK",
		"serviceTag":   "OVMS",
		"itemConfig": map[string]any{
			"type": "standalone",
		},
		"itemValue": b.ctx.req.Bandwidth,
	}
}

// getVersionByFlavorType GPU_N_PI7 规格类型特殊处理，赋值version，加到询价参数中
func (b *bms) getVersionByFlavorType() (version string) {
	if b.ctx.flavor["flavorType"] != "GPU_N_PI7" {
		return
	}
	// gpu主机定制：当admin系统配置GPU_PI7_V2为"1"，
	// 或GPU_PI7_V2为"2"并且客户扩展属性中试用产品包含"GPU_PI7_V2"时，version为"v2"
	// TODO: admin get config GPU_PI7_V2, 后续维护到 apollo 中?
	cfg, err := starship_openapi.GetOpenAPI().GetAdminConfig(b.ctx.ctx, &stmod.GetAdminConfigRequest{Key: "GPU_PI7_V2"})
	if err != nil {
		return
	}

	gpuP17V2 := cfg.ReturnObj

	customInfo := b.ctx.ctx.Value(constant.CustomInfo).(map[string]string)
	rsp, err := starship_openapi.GetOpenAPI().GetUserTryKey(b.ctx.ctx, &stmod.GetUserTryKeyRequest{
		RegionId:   b.ctx.req.RegionID,
		CustomerId: customInfo[constant.AccountId],
	})
	if err != nil {
		return
	}
	trialProducts := rsp.ReturnObj
	if gpuP17V2 == "1" || (gpuP17V2 == "2" && slices.Contains(trialProducts, "GPU_PI7_V2")) {
		version = "v2"
	} else {
		version = "v1"
	}

	return
}
