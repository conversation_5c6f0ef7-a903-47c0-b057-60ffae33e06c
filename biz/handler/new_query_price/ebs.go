package new_query_price

import (
	"strings"

	"github.com/spf13/cast"
)

type ebs struct {
	ctx *queryNewOrderPriceContext
}

func (e *ebs) buildItem() []map[string]any {
	return []map[string]any{
		{
			"resourceType": "EBS",
			"serviceTag":   "OVMS",
			"master":       true,
			"itemConfig": map[string]any{
				"dataVolumes": map[string]any{
					"type":     strings.TrimSpace(e.ctx.req.DiskType),
					"diskMode": strings.TrimSpace(e.ctx.req.DiskMode),
					"size":     cast.ToInt(e.ctx.req.DiskSize),
				},
				"volumeName": "",
				"platformId": e.ctx.req.RegionID,
				"regionId":   e.ctx.regionInfo.CtyunId,
				"volumeType": strings.TrimSpace(e.ctx.req.DiskType),
				"volumeSize": cast.ToInt(e.ctx.req.DiskSize),
			},
			"itemValue": cast.ToInt(e.ctx.req.DiskSize),
		},
	}
}
