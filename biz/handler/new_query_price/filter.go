package new_query_price

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	ec "work.ctyun.cn/git/openapi/openapi-base/error_code"
	"work.ctyun.cn/git/openapi/openapi-base/openapi/fundation_openapi"
	fdmod "work.ctyun.cn/git/openapi/openapi-base/openapi/fundation_openapi/model"
	"work.ctyun.cn/git/openapi/openapi-base/openapi/itproxy_openapi"
	itmod "work.ctyun.cn/git/openapi/openapi-base/openapi/itproxy_openapi/model"
	"work.ctyun.cn/git/openapi/openapi-base/openapi/starship_openapi"
	stmod "work.ctyun.cn/git/openapi/openapi-base/openapi/starship_openapi/model"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/filter"

	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/constant"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/model"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/util"
	"work.ctyun.cn/git/public/xone/xapollo"
)

func (q *queryNewOrderPriceContext) Filter(ctx context.Context, req *model.QueryPriceRequest,
	regionInfo *model.RegionInfoFilter) (err *ec.BizError) {
	q.ctx = ctx
	q.req = req
	q.regionInfo = regionInfo

	dictionaryJson := xapollo.GetContent(xapollo.NS("dictionary.json"))
	q.dictionary = make(map[string]any)
	if e := json.Unmarshal([]byte(dictionaryJson), &q.dictionary); e != nil {
		return util.ErrMsgTransBizErr(util.ErrApolloDictionaryFailed)
	}
	// 资源类型，key为外部传参，value为内部使用
	resourceTypeMappingAny, ok1 := q.dictionary["resourceType"]
	resourceTypeMapping, ok2 := resourceTypeMappingAny.(map[string]any)
	if !ok1 || !ok2 {
		return util.ErrMsgTransBizErr(util.ErrApolloDictionaryFailed)
	}
	if err = q.loadCustomerInfo(ctx); err != nil {
		return
	}

	switch resourceTypeMapping[q.req.ResourceType] {
	// 磁盘新下单询价过滤
	case constant.OrderTypeEbs:
		return util.CheckIfBizError(
			q.countFilter(20),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.ebsTypeChecker(),
			q.ebsModeChecker(),
			q.ebsDiskSizeFilter(10, 2048, 32768),
		)
	// IP新下单询价过滤
	case constant.OrderTypeIp:
		return util.CheckIfBizError(
			q.countFilter(10),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.ipSizeFilter(),
		)
	// 云主机新下单过滤
	case constant.OrderTypeVm:
		return util.CheckIfBizError(
			q.countFilter(50),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.vmFlavorFilter(ctx),
			q.vmImageFilter(ctx),
			q.vmSysDiskFilter(),
			q.ipSizeFilter(),
			q.vmDiskFilter(),
		)
	// NAT网关新下单询价过滤
	case constant.OrderTypeNat:
		return util.CheckIfBizError(
			q.countFilter(10),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.natTypeFilter(),
		)
	// 共享带宽新下单询价过滤
	case constant.OrderTypeIpPool:
		return util.CheckIfBizError(
			q.countFilter(10),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.ipPoolSizeFilter(),
		)
	// 物理机新下单询价过滤
	case constant.OrderTypeBms:
		return util.CheckIfBizError(
			q.countFilter(50),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.bmsAzNameFilter(),
			q.bmsFlavorFilter(ctx),
			q.bmsOrderDiskFilter(),
		)
	// 性能保障型负载均衡新下单询价过滤
	case constant.OrderTypePgElb:
		return util.CheckIfBizError(
			q.countFilter(10),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.pgelbTypeFilter(),
		)
	// 云主机备份存储库新下单询价过滤
	case constant.OrderTypeCbrVm:
		return util.CheckIfBizError(
			q.countFilter(10),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.cbrValueFilter(),
		)
	// 云硬盘备份存储库新下单询价过滤
	case constant.OrderTypeCbrVbs:
		return util.CheckIfBizError(
			q.countFilter(10),
			q.queryPriceCycleFilter(ctx, 60, 5),
			q.cbrValueFilter(),
		)
	}

	return
}

func (q *queryNewOrderPriceContext) countFilter(maxCount int) func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.Count < 1 || q.req.Count > maxCount {
			extraMessage := fmt.Sprintf("应该在1~%v之间", maxCount)
			extraMessageDesc := fmt.Sprintf("count should in range [1, %v]", maxCount)
			options := util.WithMessage(extraMessage, extraMessageDesc)
			return util.ErrMsgTransBizErr(util.ErrOrderCount.Apply(options))
		}
		return
	}
}

func (q *queryNewOrderPriceContext) queryPriceCycleFilter(
	ctx context.Context, maxMonth, maxYear int) func() (err *ec.BizError) {
	return func() (err *ec.BizError) {
		var (
			cycleType  string
			cycleCount int
		)
		// 检查用户是否按需
		if *q.req.OnDemand {
			// 非分销渠道，才进行按需校验
			if q.getOrderSource(ctx) != constant.DistrOrderSource {
				isPermitOnDemand, e := q.isPermitOnDemand()
				if e != nil {
					return e
				}
				if !isPermitOnDemand {
					return util.ErrMsgTransBizErr(util.ErrUserOnDemandForbidden)
				}
			}
			// 按需询价的时候查询 1小时 金额
			cycleType = constant.OnDemandCycleType["HOUR"]
			cycleCount = 1
			// nat按需询价最低按天
			if slices.Contains(constant.NatSlice, q.req.ResourceType) {
				cycleType = constant.OnDemandCycleType["DAY"]
			}
			return
		}

		// 判断订购周期类型
		if _, ok := constant.CycleType[q.req.CycleType]; !ok {
			return util.ErrMsgTransBizErr(util.ErrCycleTypeFailed)
		}
		if q.req.CycleCount < 1 {
			return util.ErrMsgTransBizErr(util.ErrCycleCountFailed)
		}
		// 判断订购周期范围
		switch {
		case q.req.CycleType == "MONTH" && q.req.CycleCount > maxMonth:
			extraMessage := fmt.Sprintf("订购周期最大为 %v 个月", maxMonth)
			extraMessageDesc := fmt.Sprintf("cycleCount max is %v months", maxMonth)
			return util.ErrMsgTransBizErr(util.ErrOrderCount.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		case q.req.CycleType == "YEAR" && q.req.CycleCount > maxYear:
			extraMessage := fmt.Sprintf("订购周期最大为 %v 年", maxYear)
			extraMessageDesc := fmt.Sprintf("cycleCount max is %v years", maxYear)
			return util.ErrMsgTransBizErr(util.ErrOrderCount.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		// 不支持折扣的资源池和产品处理
		noDiscountCycleType, noDiscountCycleCount, err :=
			q.isNoDiscountForRegion(ctx, q.req.ResourceType, q.req.CycleType, q.req.CycleCount)
		if err != nil {
			return
		}
		if noDiscountCycleType != "" && noDiscountCycleCount != 0 {
			cycleType = noDiscountCycleType
			cycleCount = noDiscountCycleCount
		}
		if cycleType != "YEAR" {
			return
		}

		if (slices.Contains([]string{constant.OrderTypeBms, constant.OrderTypePgElb}, q.req.ResourceType) && cycleCount <= 3) ||
			slices.Contains([]string{constant.OrderTypeEbs, constant.OrderTypeVm}, q.req.ResourceType) {
			exYear := constant.YearType[cast.ToString(cycleCount)]
			cycleType = constant.CycleType[exYear]
			cycleCount = 1
		}
		return
	}
}

// ebsTypeChecker 云盘类型检查
func (q *queryNewOrderPriceContext) ebsTypeChecker() func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.DiskType == constant.NullStr {
			extraMessage := "diskType不能为空"
			extraMessageDesc := "diskType cannot be empty"
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		diskTypeListAny, _ := q.dictionary["diskType"]
		diskTypeList, _ := diskTypeListAny.([]string)
		if !slices.Contains(diskTypeList, q.req.DiskType) {
			extraMessage := fmt.Sprintf("不支持 %s 磁盘类型", q.req.DiskType)
			extraMessageDesc := fmt.Sprintf("not support %s diskType", q.req.DiskType)
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}

		// \"storage_type\": \"SATA,SAS,SSD-genric,SSD,FAST-SSD,XSSD-0,XSSD-1,XSSD-2\"
		regionDiskTypeList := strings.Split(cast.ToString(q.regionInfo.ExtraDetails["storage_type"]), ",")
		if !slices.Contains(regionDiskTypeList, q.req.DiskType) {
			extraMessage := fmt.Sprintf("[%v]资源池仅支持%v类型云硬盘", q.req.RegionID, regionDiskTypeList)
			extraMessageDesc := fmt.Sprintf("[%v]region only supports%vdisk types", q.req.RegionID, regionDiskTypeList)
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}

		// 新增x系列云硬盘的diskSize范围与普通盘不同，单独列出来，将磁盘范围与v4/ebs/new-ebs接口对齐
		if slices.Contains(constant.XssdDiskType, q.req.DiskType) {
			q.diskMinSize = constant.XssdDiskSize[q.req.DiskType][0]
			q.diskMaxSize = constant.XssdDiskSize[q.req.DiskType][1]
		} else {
			q.diskMinSize = constant.OtherDiskSize[0]
			q.diskMaxSize = constant.OtherDiskSize[1]
		}

		return
	}
}

// ebsModeChecker diskMode合法性检查
func (q *queryNewOrderPriceContext) ebsModeChecker() func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.DiskMode == constant.NullStr {
			extraMessage := "diskMode不能为空"
			extraMessageDesc := "diskMode cannot be empty"
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		validDiskModeListAny, _ := q.dictionary["diskMode"]
		validDiskModeList, _ := validDiskModeListAny.([]string)

		if !slices.Contains(validDiskModeList, q.req.DiskMode) {
			extraMessage := fmt.Sprintf("不支持 %v 磁盘模式", validDiskModeList)
			extraMessageDesc := fmt.Sprintf("not support %v diskMode", validDiskModeList)
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if q.req.DiskMode == "ISCSI" || q.req.DiskMode == "FCSAN" {
			flagName := "has" + q.req.DiskMode
			checkFlagValue, ok := q.regionInfo.ExtraDetails[flagName]
			if !ok || checkFlagValue == "0" {
				extraMessage := fmt.Sprintf("[%s]资源池不支持%s类型数据盘", q.req.RegionID, q.req.DiskMode)
				extraMessageDesc := fmt.Sprintf("[%s] region not support %s diskMode", q.req.RegionID, q.req.DiskMode)
				return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
			return
		}
		return
	}

}

func (q *queryNewOrderPriceContext) ebsDiskSizeFilter(minSize, maxSysSize, maxDiskSize int) func() *ec.BizError {
	return func() (err *ec.BizError) {
		// TODO 这个接口在new和renew都会被调用，暂时只做new的相关判断，renew后面再加，本质是调用的时候规定了是renew/new
		// /order/new-query-price /new-order/query-price ->new
		// /renew-order/query-price /order/renew-query-price -> renew
		// /upgrade-order/query-price /order/upgrade-query-price ->upgrade
		if q.req.DiskSize == nil {
			extraMessage := "磁盘大小不能为空且类型需为整型"
			extraMessageDesc := "diskSize cannot be empty and parameter type must be integer"
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}

		// 包周期云硬盘大小 变配的时候不能比现在的小
		if q.queryOrderType == constant.QueryOrderTypeUpgrade && *q.req.OnDemand {
			if *q.req.DiskSize <= q.diskMinSize {
				extraMessage := fmt.Sprintf("云硬盘大小diskSize应该大于当前大小%v", q.diskMinSize)
				extraMessageDesc := "The diskSize should be greater than the current size"
				return util.ErrMsgTransBizErr(util.ErrEbsSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
		}
		if q.isSysDisk {
			// 若为系统盘
			if *q.req.DiskSize < minSize || *q.req.DiskSize > maxSysSize {
				extraMessage := fmt.Sprintf("云硬盘系统盘的大小范围为%v~%vGB之间", minSize, maxSysSize)
				extraMessageDesc := fmt.Sprintf("system disk size should in range [%vG, %vG]", minSize, maxSysSize)
				return util.ErrMsgTransBizErr(util.ErrEbsSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
		} else {
			// 若为数据盘
			if slices.Contains(constant.XssdDiskType, q.req.DiskType) {
				// 针对新增的三种类型数据盘的范围
				if *q.req.DiskSize < q.diskMinSize || *q.req.DiskSize > q.diskMaxSize {
					extraMessage := fmt.Sprintf("云硬盘数据盘的大小范围为%v~%vGB之间", q.diskMinSize, q.diskMaxSize)
					extraMessageDesc := fmt.Sprintf("data disk size should in range [%vG, %vG]", q.diskMinSize, q.diskMaxSize)
					return util.ErrMsgTransBizErr(util.ErrEbsSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
				}
			} else {
				// 原有数据盘类型
				if *q.req.DiskSize < minSize || *q.req.DiskSize > maxDiskSize {
					extraMessage := fmt.Sprintf("云硬盘数据盘的大小范围为%v~%vGB之间", q.diskMinSize, q.diskMaxSize)
					extraMessageDesc := fmt.Sprintf("data disk size should in range [%vG, %vG]", q.diskMinSize, q.diskMaxSize)
					return util.ErrMsgTransBizErr(util.ErrEbsSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
				}
			}
		}
		return
	}
}

// ipSizeFilter 弹性公网IP带宽大小检查, 检查云主机成功订购时是否选择了弹性IP，和单独订购弹性IP
// 只在upgrade时current有真实值，否则为0
func (q *queryNewOrderPriceContext) ipSizeFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		// 主机成套和单买弹性ip都是这个字段
		if q.req.Bandwidth == nil {
			extraMessage := "带宽大小bandwidth类型不正确,需为整型"
			extraMessageDesc := "bandwidth type is incorrect, parameter type must be integer"
			return util.ErrMsgTransBizErr(util.ErrParamErr.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if *q.req.Bandwidth == 0 {
			extraMessage := fmt.Sprintf("带宽大小应该在 %v~2000之间", constant.IPMinBandWidth)
			extraMessageDesc := fmt.Sprintf("bandwidth should in range [%v, 2000]", constant.IPMinBandWidth)
			return util.ErrMsgTransBizErr(util.ErrBandwidth.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		// 云主机调用此函数时，如果没有选择弹性公网ip可以不传此字段
		if q.req.ResourceType == "VM" && q.req.Bandwidth == nil {
			return
		}
		if *q.req.Bandwidth < constant.IPMinBandWidth || *q.req.Bandwidth > 2000 {
			extraMessage := fmt.Sprintf("带宽大小应该在 %v~2000之间", constant.IPMinBandWidth)
			extraMessageDesc := fmt.Sprintf("bandwidth should in range [%v, 2000]", constant.IPMinBandWidth)
			return util.ErrMsgTransBizErr(util.ErrBandwidth.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		// 包周期弹性公网ip贷款变配的时候不能比现有带宽小,只在upgrade时current有真实值，否则为0
		if q.queryOrderType == constant.QueryOrderTypeUpgrade && !*q.req.OnDemand {
			if *q.req.Bandwidth < q.ipCurrentBandwidth {
				extraMessage := fmt.Sprintf("包周期的弹性公网IP大小应该大于当前带宽%v", q.ipCurrentBandwidth)
				extraMessageDesc := "bandwidth should greater than current bandwidth"
				return util.ErrMsgTransBizErr(util.ErrBandwidth.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
		}
		return
	}
}

// vmFlavorFilter 主机规格校验
func (q *queryNewOrderPriceContext) vmFlavorFilter(ctx context.Context) func() *ec.BizError {
	return func() (err *ec.BizError) {
		// 原逻辑中使用 &product=console 的 query 参数, 但实际无作用, 看 admin 代码就是获取的 cache key 不同
		specList, ok := util.GetVmspecsQuery(ctx, q.req.RegionID).([]map[string]any)
		if !ok {
			extraMessage := "admin查询规格内部错误，请重试。如果多次尝试失败，请提交工单。"
			extraMessageDesc := "The request processing has failed due to some unknown error."
			return util.ErrMsgTransBizErr(util.ErrAdminQuery.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		// 资源类型为VM时，flavorName为必填
		flavorName := strings.TrimSpace(q.req.FlavorName)
		if flavorName == constant.NullStr {
			extraMessage := "When resourceType is VM, flavorName cannot be empty"
			extraMessageDesc := "当resourceType为VM时,flavorName不能为空"
			return util.ErrMsgTransBizErr(util.ErrVMFlavor.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		specMap := make(map[string]map[string]any, len(specList))
		for _, spec := range specList {
			specMap[cast.ToString(spec["spec_name"])] = map[string]any{
				"flavorType": spec["flavor_type"],
				"name":       spec["spec_name"],
				"memSize":    spec["ram"],
				"cpuNum":     spec["vcpu"],
			}
		}
		q.flavor, ok = specMap[flavorName]
		if !ok {
			extraMessage := "该规格flavorName不存在，请检查规格名称是否正确"
			extraMessageDesc := "flavor name is not exist"
			return util.ErrMsgTransBizErr(util.ErrVMFlavor.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		return
	}
}

func (q *queryNewOrderPriceContext) vmImageFilter(ctx context.Context) func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.ImageUUID == nil || strings.TrimSpace(*q.req.ImageUUID) == constant.NullStr {
			extraMessage := "imageUUID传参类型有误,且当resourceType为VM时不能为null"
			extraMessageDesc := "The imageUUID type is incorrect and cannot be null when resourceType is VM"
			return util.ErrMsgTransBizErr(util.ErrVMImage.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		imageUUID := strings.TrimSpace(*q.req.ImageUUID)
		customInfo := ctx.Value(constant.CustomInfo).(map[string]string)
		// admin的获取images的接口没有重构，继续沿用
		rsp, e := starship_openapi.GetOpenAPI().GetImagesInfo(ctx, &stmod.GetImagesInfoRequest{
			AccountId: customInfo[constant.AccountId],
			RegionId:  q.req.RegionID,
			ImageUUID: imageUUID,
			Flavor:    cast.ToString(q.flavor["name"]),
		})
		if e != nil {
			extraMessage := "admin查询镜像内部错误，请重试。如果多次尝试失败，请提交工单。"
			extraMessageDesc := "The request processing has failed due to some unknown error."
			return util.ErrMsgTransBizErr(util.ErrAdminQuery.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		imageList := cast.ToSlice(rsp.ReturnObj["result"])
		if len(imageList) == 0 {
			return
		}
		imageInf, ok := imageList[0].(map[string]any)
		if !ok {
			return
		}
		// 取值1/0，不一定有值，没有值按0处理，即不支持
		q.ctySupportSSD = cast.ToBool(imageInf["support_ssd"])
		return
	}
}

func (q *queryNewOrderPriceContext) vmSysDiskFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.SysDiskType == nil || *q.req.SysDiskType == constant.NullStr {
			extraMessage := "当resourceType为VM时，系统盘类型sysDiskType不能为空"
			extraMessageDesc := "sysDiskType cannot be empty when resourceType is VM"
			return util.ErrMsgTransBizErr(util.ErrVMType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		// 系统盘类型检查
		regionDiskTypeList := strings.Split(cast.ToString(q.regionInfo.ExtraDetails["storage_type"]), ",")
		if slices.Contains(regionDiskTypeList, *q.req.SysDiskType) {
			extraMessage := fmt.Sprintf("[%v]资源池仅支持%v类型系统盘", q.req.RegionID, regionDiskTypeList)
			extraMessageDesc := fmt.Sprintf("[%v]region only supports%vdisk types", q.req.RegionID, regionDiskTypeList)
			return util.ErrMsgTransBizErr(util.ErrVMType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if q.req.SysDiskSize == nil {
			extraMessage := fmt.Sprintf("系统盘大小sysDiskSize不能为空且类型需为整型")
			extraMessageDesc := fmt.Sprintf("sysDiskSize cannot be empty and parameter type must be integer")
			return util.ErrMsgTransBizErr(util.ErrParamErr.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if *q.req.SysDiskSize < constant.SysDiskSize[0] || *q.req.SysDiskSize > constant.SysDiskSize[1] {
			return util.ErrMsgTransBizErr(util.ErrVMSize)
		}
		return
	}
}

// vmDiskFilter 数据盘校验
func (q *queryNewOrderPriceContext) vmDiskFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		// 可以不要数据盘
		if len(q.req.Disks) == 0 {
			return
		}
		// 获取当前region支持的数据盘类型，但具体的az可能会有额外的数据盘支持
		regionDiskTypeList := strings.Split(cast.ToString(q.regionInfo.ExtraDetails["storage_type"]), ",")
		// 只有4.0存在xssd的支持，3.0仅使用region进行判断即可
		if !q.regionInfo.IsMultiZones {
			for _, disk := range q.req.Disks {
				if err = q.diskTypeFilter(regionDiskTypeList, *disk.DiskType); err != nil {
					return
				}
			}
		}

		flavorName := strings.Split(q.req.FlavorName, ",")[0]

		// 4.0资源池则还存在az+imageUuid的判断
		// 根据image类型判断当前主机类型是否可以添加XSSD类型的数据盘
		var storageTypeList, xssdFlavorTypeList []string
		if q.ctySupportSSD {
			// 添加当前az支持的额外的数据盘类型
			storageTypeList, xssdFlavorTypeList = q.getXssdAvailableList(q.req.AzName)
		}
		for _, disk := range q.req.Disks {
			if disk.DiskSize == nil || disk.DiskType == nil {
				extraMessage := "数据盘disks内部字段类型错误"
				extraMessageDesc := "disks member type is error"
				return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
			switch {
			// 若数据盘类型为特殊类型且该镜像不支持xssd
			case slices.Contains(constant.XssdDiskType, *disk.DiskType) && !q.ctySupportSSD:
				extraMessage := "该镜像不支持当前数据盘类型"
				extraMessageDesc := "this image does not support these data disks type."
				return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			// 若数据盘类型为特殊类型且该镜像支持xssd，则进行特殊校验，否则只按照region进行校验
			case slices.Contains(constant.XssdDiskType, *disk.DiskType) && q.ctySupportSSD:
				// 4.0资源池必须带azName，且azName必须在当前资源池的azList当中，
				// 这个判断当且仅当新增xssd数据盘时生效，避免影响此前无xssd的接口
				if q.req.AzName == constant.NullStr {
					extraMessage := "针对多可用区的资源池的询价，请求必须带可用区名字"
					extraMessageDesc := "azName cannot be empty"
					return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
				}
				// 如果diskType是xssd，需要判断该主机规格是否支持
				// 如"flavorName": "s8.large.2",此时xssd_flavor_types是[c8ne,m8ne,c8]则不支持
				if err = q.diskXssdTypeFilter(storageTypeList, xssdFlavorTypeList, *disk.DiskType, flavorName); err != nil {
					return
				}
			default:
				if err = q.diskTypeFilter(regionDiskTypeList, *disk.DiskType); err != nil {
					return
				}
			}

			if err = q.diskSizeFilter(*disk.DiskSize, *disk.DiskType); err != nil {
				return
			}
		}
		return
	}
}

// natTypeFilter NAT网关规格检查
func (q *queryNewOrderPriceContext) natTypeFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		natType := strings.TrimSpace(q.req.NatType)
		if natType == constant.NullStr {
			extraMessage := "当resourceType为NAT时,NAT网关规格natType不能为空"
			extraMessageDesc := "natType cannot be empty when resourceType is NAT"
			return util.ErrMsgTransBizErr(util.ErrNATType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if !slices.Contains(constant.NatType, q.req.NatType) {
			extraMessage := fmt.Sprintf("NAT规格应该在%v之中", constant.NatType)
			extraMessageDesc := fmt.Sprintf("NAT type should be in %v", constant.NatType)
			return util.ErrMsgTransBizErr(util.ErrNATType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if q.queryOrderType == constant.QueryOrderTypeUpgrade && !*q.req.OnDemand {
			if constant.NatTypeMap[natType] <= constant.NatTypeMap[q.natCurrentType] {
				extraMessage := "包周期的NAT规格要高于当前规格，不能降配"
				extraMessageDesc := "The natType should be higher than the current specification and cannot be downgraded"
				return util.ErrMsgTransBizErr(util.ErrNATType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
		}
		return
	}
}

// ipPoolSizeFilter 共享带宽大小检查
func (q *queryNewOrderPriceContext) ipPoolSizeFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.IPPoolBandwidth == nil {
			extraMessage := "带宽大小不能为空且类型需为整型"
			extraMessageDesc := "ipPoolBandwidth cannot be empty and parameter type must be integer"
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		// 包周期 变配的时候不能比现有带宽小
		if q.queryOrderType == constant.QueryOrderTypeUpgrade && !*q.req.OnDemand {
			if *q.req.IPPoolBandwidth <= q.ipPoolCurrentBandwidth {
				extraMessage := "包周期的共享带宽大小应该大于当前带宽"
				extraMessageDesc := "ipPoolBandwidth should greater than current bandwidth"
				return util.ErrMsgTransBizErr(util.ErrIpPoolBandwidth.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
		}

		if *q.req.IPPoolBandwidth < constant.IPPoolBandWidth[0] || *q.req.IPPoolBandwidth > constant.IPPoolBandWidth[1] {
			extraMessage := fmt.Sprintf("带宽大小范围为%v~%v", constant.IPPoolBandWidth[0], constant.IPPoolBandWidth[1])
			extraMessageDesc := fmt.Sprintf("bandwidth size should in range [%v, %v]", constant.IPPoolBandWidth[0], constant.IPPoolBandWidth[1])
			return util.ErrMsgTransBizErr(util.ErrBandwidth.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		return
	}
}

func (q *queryNewOrderPriceContext) bmsAzNameFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		azName := strings.TrimSpace(q.req.AzName)
		if azName == constant.NullStr {
			extraMessage := "azName不能为空"
			extraMessageDesc := "azName cannot be empty"
			return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		for _, az := range q.regionInfo.AzList {
			if az.AzName == azName {
				return
			}
		}

		extraMessage := "资源池中不存在该azName"
		extraMessageDesc := "specified azName not exists in this region"
		return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}
}

// bmsFlavorFilter 物理机规格校验
func (q *queryNewOrderPriceContext) bmsFlavorFilter(ctx context.Context) func() *ec.BizError {
	return func() (err *ec.BizError) {
		flavor, err := q.getBmsFlavor(ctx)
		if err != nil {
			return
		}

		q.bmsMemSize = cast.ToInt(flavor["mem_size"]) * cast.ToInt(flavor["mem_amount"])
		q.bmsCpuNum = fmt.Sprintf("%d路%d核", cast.ToInt(flavor["cpu_sockets"]), cast.ToInt(flavor["cpu_amount"]))
		q.bmsSyshd = cast.ToInt(flavor["system_volume_size"]) * cast.ToInt(flavor["system_volume_amount"])
		q.bmsSyshdType = cast.ToString(flavor["system_volume_type"])
		q.bmsDatahd = cast.ToInt(flavor["data_volume_size"]) * cast.ToInt(flavor["data_volume_amount"])
		q.bmsDatahdType = cast.ToString(flavor["data_volume_type"])
		q.bmsNvmehd = cast.ToInt(flavor["nvme_volume_size"]) * cast.ToInt(flavor["nvme_volume_amount"])
		q.bmsNvmehdType = cast.ToString(flavor["nvme_volume_type"])
		q.bmsType = "CPU_" + cast.ToString(flavor["cpu_manufacturer"])

		gpuCount := cast.ToInt(flavor["gpu_count"])
		if gpuCount != 0 {
			q.bmsType = fmt.Sprintf("GPU_%s_%v",
				cast.ToString(flavor["gpu_model"]), cast.ToInt(flavor["gpu_size"])*gpuCount)
		}
		q.bmsProductType = cast.ToString(flavor["project"])
		if q.bmsProductType == constant.NullStr {
			q.bmsProductType = "standard"
		}

		// 构建bms rdma type
		computeIBAmount := cast.ToInt(flavor["compute_ib_amount"])
		computeIBRate := cast.ToInt(flavor["compute_ib_rate"])
		storageIBAmount := cast.ToInt(flavor["storage_ib_amount"])
		storageIBRate := cast.ToInt(flavor["storage_ib_rate"])
		computeROCEAmount := cast.ToInt(flavor["compute_roce_amount"])
		computeROCERate := cast.ToInt(flavor["compute_roce_rate"])
		storageROCEAmount := cast.ToInt(flavor["storage_roce_amount"])
		storageROCERate := cast.ToInt(flavor["storage_roce_rate"])

		bmsRDMATypeList := make([]string, 0, 4)
		if computeIBAmount*computeIBRate > 0 {
			bmsRDMATypeList = append(bmsRDMATypeList,
				fmt.Sprintf("computeIB%d*%d", computeIBAmount, computeIBRate))
		}
		if storageIBAmount*storageIBRate > 0 {
			bmsRDMATypeList = append(bmsRDMATypeList,
				fmt.Sprintf("storageIB%d*%d", storageIBAmount, storageIBRate))
		}
		if computeROCEAmount*computeROCERate > 0 {
			bmsRDMATypeList = append(bmsRDMATypeList,
				fmt.Sprintf("computeRoCE%d*%d", computeROCEAmount, computeROCERate))
		}
		if storageROCEAmount*storageROCERate > 0 {
			bmsRDMATypeList = append(bmsRDMATypeList,
				fmt.Sprintf("storageRoCE%d*%d", storageROCEAmount, storageROCERate))
		}

		if len(bmsRDMATypeList) > 0 {
			q.bmsRDMAType = strings.Join(bmsRDMATypeList, "+")
		} else {
			q.bmsRDMAType = "0"
		}

		return
	}
}

// bmsOrderDiskFilter 物理机云硬盘校验
func (q *queryNewOrderPriceContext) bmsOrderDiskFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		// 选填
		if len(q.req.OrderDisks) == 0 {
			return
		}
		regionDiskTypeList := strings.Split(cast.ToString(q.regionInfo.ExtraDetails["storage_type"]), ",")
		for _, disk := range q.req.OrderDisks {
			if disk.DiskSize == nil || disk.DiskType == nil {
				extraMessage := "orderDisks内部字段类型错误"
				extraMessageDesc := "orderDisks member type is error"
				return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
			if err = q.diskTypeFilter(regionDiskTypeList, *disk.DiskType); err != nil {
				return
			}
			if err = q.diskSizeFilter(*disk.DiskSize, *disk.DiskType); err != nil {
				return
			}
		}
		return
	}
}

// pgelbTypeFilter PGELB 网关规格检查
func (q *queryNewOrderPriceContext) pgelbTypeFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.ElbType == nil || *q.req.ElbType == constant.NullStr {
			extraMessage := "当resourceType为PGELB时,性能保障型负载均衡类型elbType不能为空且类型需为String类型"
			extraMessageDesc := "When resourceType is PGELB, elbType cannot be empty and parameter type must be string"
			return util.ErrMsgTransBizErr(util.ErrPgelbType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if !slices.Contains(constant.PgelbType, *q.req.ElbType) {
			extraMessage := fmt.Sprintf("性能保障型负载均衡类型应该在%v之中", constant.PgelbType)
			// FIXME: openapi-mono 中就是 elbType should in 而不是 Pgelb type should be in
			extraMessageDesc := fmt.Sprintf("elbType should in %v", constant.PgelbType)
			return util.ErrMsgTransBizErr(util.ErrPgelbType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if q.queryOrderType == constant.QueryOrderTypeUpgrade && !*q.req.OnDemand {
			if constant.PgelbTypeMap[*q.req.ElbType] <= constant.PgelbTypeMap[q.currentPGELBType] {
				extraMessage := "包周期的性能保障型负载均衡类型规格要高于当前类型规格，不能降配"
				extraMessageDesc := "The elbType should be higher than the current specification and cannot be downgraded"
				return util.ErrMsgTransBizErr(util.ErrPgelbType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
			}
		}
		return
	}
}

// cbrValueFilter 检查存储库大小
func (q *queryNewOrderPriceContext) cbrValueFilter() func() *ec.BizError {
	return func() (err *ec.BizError) {
		if q.req.CbrValue == nil {
			extraMessage := "当resourceType为CBR_VM或CBR_VBS时,存储库大小cbrValue不能为空且类型需为整型"
			extraMessageDesc := "When resourceType is CBR_VM or CBR_VBS,cbrValue cannot be empty and parameter type must be integer"
			return util.ErrMsgTransBizErr(util.ErrCbrSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		if q.queryOrderType == constant.QueryOrderTypeUpgrade && !*q.req.OnDemand {
			if *q.req.CbrValue <= q.cbrSize {
				return util.ErrMsgTransBizErr(util.ErrCbrSize)
			}
		}

		if *q.req.CbrValue < constant.CbrValue[0] || *q.req.CbrValue > constant.CbrValue[1] {
			extraMessage := fmt.Sprintf("存储库的大小为%v~%vGB", constant.CbrValue[0], constant.CbrValue[1])
			extraMessageDesc := fmt.Sprintf("cbrValue should in range [%v, %v]", constant.CbrValue[0], constant.CbrValue[1])
			return util.ErrMsgTransBizErr(util.ErrCbrSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
		}
		return
	}
}

func (q *queryNewOrderPriceContext) diskTypeFilter(regionDiskTypeList []string, diskType string) (err *ec.BizError) {
	diskType = strings.TrimSpace(diskType)
	if diskType == constant.NullStr {
		extraMessage := "disks中数据盘类型diskType不能为空"
		extraMessageDesc := "disks.diskType cannot be empty"
		return util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}
	if !slices.Contains(regionDiskTypeList, diskType) {
		extraMessage := fmt.Sprintf("资源池仅支持%v类型数据盘", regionDiskTypeList)
		extraMessageDesc := fmt.Sprintf("region only supports%vdisk types", regionDiskTypeList)
		return util.ErrMsgTransBizErr(util.ErrVMType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}
	return
}

func (q *queryNewOrderPriceContext) diskSizeFilter(diskSize int, diskType string) (err *ec.BizError) {
	var minSize, maxSize int
	if slices.Contains(constant.XssdDiskType, diskType) {
		minSize = constant.XssdDiskSize[diskType][0]
		maxSize = constant.XssdDiskSize[diskType][1]
	} else {
		minSize = constant.OtherDiskSize[0]
		maxSize = constant.OtherDiskSize[1]
	}

	if diskSize < minSize || diskSize > maxSize {
		extraMessage := fmt.Sprintf("单块数据盘的大小范围为%v-%vGB", minSize, maxSize)
		extraMessageDesc := fmt.Sprintf("disks.diskSize should in range [%v, %v]", minSize, maxSize)
		return util.ErrMsgTransBizErr(util.ErrVMSize.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}

	return
}

func (q *queryNewOrderPriceContext) diskXssdTypeFilter(storageTypeList, xssdFlavorTypeList []string,
	diskType, flavorName string) (err *ec.BizError) {
	if !slices.Contains(xssdFlavorTypeList, flavorName) {
		extraMessage := fmt.Sprintf("该资源池可用区的该主机规格不支持 %v 类型的云硬盘", diskType)
		extraMessageDesc := fmt.Sprintf("The VM flavor does not support %v type disk", diskType)
		return util.ErrMsgTransBizErr(util.ErrVMType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}
	if !slices.Contains(storageTypeList, diskType) {
		extraMessage := fmt.Sprintf("该资源池可用区规格不支持 %v 类型的云硬盘", diskType)
		extraMessageDesc := fmt.Sprintf("The region AZ does not support %v type disk", diskType)
		return util.ErrMsgTransBizErr(util.ErrVMType.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}
	return
}

func (q *queryNewOrderPriceContext) getXssdAvailableList(azName string) (storageTypeList, xssdFlavorTypeList []string) {
	// 根据azName获得对应的azInfo，进而获得该az支持那些storage type
	// 根据azName拿到当前az支持的XSSDFlavorType，若数据盘是xssd类型，则在上级调用的函数进行判断
	for _, azInfo := range q.regionInfo.AzList {
		if azName != azInfo.AzName {
			continue
		}
		// "storage_type": "SATA,SAS,SSD-genric,SSD,FAST-SSD,XSSD-0,XSSD-1,XSSD-2"
		if storageType, ok := azInfo.Details["storage_type"]; ok {
			storageTypeList = strings.Split(cast.ToString(storageType), ",")
		}
		// "XSSDFlavorType": "c8ne,m8ne,c8"
		if xssdFlavorType, ok := azInfo.Details["XSSDFlavorType"]; ok {
			xssdFlavorTypeList = strings.Split(cast.ToString(xssdFlavorType), ", ")
		}
		return
	}
	return
}

func (q *queryNewOrderPriceContext) getOrderSource(ctx context.Context) int {
	delegatePolicy, _ := filter.GetDelegatePolicy(ctx)
	roleList := delegatePolicy.RoleList
	for _, role := range roleList {
		source, exists := constant.OrderSourceDict[cast.ToString(role)]
		if exists {
			return source
		}
	}
	return constant.OpenapiOrderSource
}

func (q *queryNewOrderPriceContext) getBmsFlavor(ctx context.Context) (bmsFlavor map[string]any, err *ec.BizError) {
	deviceType := strings.TrimSpace(q.req.DeviceType)
	if deviceType == constant.NullStr {
		extraMessage := "当resourceType为BMS时,deviceType不能为空"
		extraMessageDesc := "When resourceType is BMS, deviceType cannot be empty"
		return nil, util.ErrMsgTransBizErr(util.ErrParameterEmpty.Apply(util.WithMessage(extraMessage, extraMessageDesc)))
	}

	customInfo := ctx.Value(constant.CustomInfo).(map[string]string)
	rsp, e := fundation_openapi.GetFoundationOpenAPI().GetDeviceTypes(ctx, &fdmod.GetDeviceTypesReq{
		RoutingRuleReq: &fdmod.RoutingRuleReq{HT: q.regionInfo.IsHT, Yacos40: q.regionInfo.IsMultiZones},
		AccountId:      customInfo[constant.AccountId],
		RegionId:       q.regionInfo.RegionID,
		Region:         cast.ToString(q.regionInfo.ExtraDetails["region"]),
		Az:             q.req.AzName,
		DeviceType:     deviceType,
	})
	if e != nil || len(rsp.ReturnObj) == 0 {
		return nil, util.ErrMsgTransBizErr(util.ErrBMSFlavor)
	}
	return rsp.ReturnObj[0], nil
}

func (q *queryNewOrderPriceContext) loadCustomerInfo(ctx context.Context) (err *ec.BizError) {
	customInfo := ctx.Value(constant.CustomInfo).(map[string]string)
	rsp, e := itproxy_openapi.GetOpenAPI().GetCustomerAccountInfo(ctx, &itmod.GetCustomerAccountInfoRequest{
		AccountId: customInfo[constant.AccountId],
	})
	if e != nil {
		return util.ErrMsgTransBizErr(util.ErrUserDetailAccessFailed)
	}

	if len(rsp.ReturnObj) == 0 {
		return util.ErrMsgTransBizErr(util.ErrUserDetailEmpty)
	}
	ReturnObjJsonBytes, _ := json.Marshal(rsp.ReturnObj)
	q.customerDetailGJson = util.AnyToPtr(gjson.ParseBytes(ReturnObjJsonBytes))
	return
}

func (q *queryNewOrderPriceContext) isPermitOnDemand() (onDemand bool, err *ec.BizError) {
	rs := q.customerDetailGJson.Get("accountMetaBO.isPermitOnDemand")
	if !rs.Exists() || !rs.IsBool() {
		return false, util.ErrMsgTransBizErr(util.ErrUserDetailDataDamaged)
	}
	onDemand = rs.Bool()
	return
}

// isNoDiscountForRegion 不支持折扣的资源池和产品处理，如果不支持折扣，订购周期由”年“转换为”月“
func (q *queryNewOrderPriceContext) isNoDiscountForRegion(ctx context.Context,
	resourceType, cycleType string, cycleCount int) (noDiscountCycleType string, noDiscountCycleCount int, err *ec.BizError) {
	// 判断 负载均衡是否支持包年折扣，hasElbPackYearDiscount为"1"时，或者为"2"并用户配有试用产品时，有折扣
	if resourceType == constant.OrderTypePgElb {
		customInfo := ctx.Value(constant.CustomInfo).(map[string]string)
		rsp, e := starship_openapi.GetOpenAPI().GetUserTryKey(ctx, &stmod.GetUserTryKeyRequest{
			RegionId:   q.req.RegionID,
			CustomerId: customInfo[constant.AccountId],
		})
		if e != nil {
			return "", 0, util.ErrMsgTransBizErr(util.ErrUserDetailAccessFailed)
		}
		trialProducts := rsp.ReturnObj
		hasElbPackYearDiscount := cast.ToInt(q.regionInfo.ExtraDetails["hasElbPackYearDiscount"])
		if hasElbPackYearDiscount == 1 || (
			hasElbPackYearDiscount == 2 && slices.Contains(trialProducts, "hasElbPackYearDiscount")) {
			return
		}
	}

	// 开关的键是isDiscount，取值为“1”的情况下不支持折扣，是只能按月算，包年也同样按月算
	isDiscount := cast.ToString(q.regionInfo.ExtraDetails["isDiscount"])
	// 非折扣场景判断
	if isDiscount != "1" || !slices.Contains(constant.NoDiscountResourceTypes, cycleType) {
		return
	}
	if resourceType == "YEAR" {
		cycleType = "MONTH"
		cycleCount = cycleCount * 12
	}

	noDiscountCycleType = cycleType
	noDiscountCycleCount = cycleCount
	return
}
