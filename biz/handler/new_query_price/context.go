package new_query_price

import (
	"context"

	"github.com/tidwall/gjson"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/constant"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/model"
)

type queryNewOrderPriceContext struct {
	ctx                 context.Context
	queryOrderType      constant.QueryOrderType // new, renew, upgrade
	req                 *model.QueryPriceRequest
	regionInfo          *model.RegionInfoFilter
	customerDetailGJson *gjson.Result
	dictionary          map[string]any

	// ebs
	isSysDisk   bool
	diskMinSize int
	diskMaxSize int

	// ip
	ipCurrentBandwidth int

	// vm
	flavor        map[string]any
	ctySupportSSD bool // 当前主机镜像是否支持xssd类型的数据盘

	// nat
	natCurrentType string

	// ip pool
	ipPoolCurrentBandwidth int

	// bms
	bmsMemSize     int
	bmsCpuNum      string
	bmsSyshd       int
	bmsSyshdType   string
	bmsDatahd      int
	bmsDatahdType  string
	bmsNvmehd      int
	bmsNvmehdType  string
	bmsType        string
	bmsProductType string
	bmsRDMAType    string

	// pgelb
	currentPGELBType string

	// cbr
	cbrSize int
}

type QueryNewOrderPriceFilterOptionFn func(o *queryNewOrderPriceContext)

func QueryNewOrderPriceFilterIsSysDick(isSysDisk bool) QueryNewOrderPriceFilterOptionFn {
	return func(o *queryNewOrderPriceContext) {
		o.isSysDisk = isSysDisk
	}
}

func QueryNewOrderPriceFilterIpCurrentBandwidth(ipCurrentBandwidth int) QueryNewOrderPriceFilterOptionFn {
	return func(o *queryNewOrderPriceContext) {
		o.ipCurrentBandwidth = ipCurrentBandwidth
	}
}

func QueryNewOrderPriceFilterNatCurrentType(natCurrentType string) QueryNewOrderPriceFilterOptionFn {
	return func(o *queryNewOrderPriceContext) {
		o.natCurrentType = natCurrentType
	}
}

func QueryNewOrderPriceFilterIpPoolCurrentBandwidth(ipPoolCurrentBandwidth int) QueryNewOrderPriceFilterOptionFn {
	return func(o *queryNewOrderPriceContext) {
		o.ipPoolCurrentBandwidth = ipPoolCurrentBandwidth
	}
}

func QueryNewOrderPriceFilterCurrentPGELBType(currentPGELBType string) QueryNewOrderPriceFilterOptionFn {
	return func(o *queryNewOrderPriceContext) {
		o.currentPGELBType = currentPGELBType
	}
}

func QueryNewOrderPriceFilterCbrSize(cbrSize int) QueryNewOrderPriceFilterOptionFn {
	return func(o *queryNewOrderPriceContext) {
		o.cbrSize = cbrSize
	}
}

func NewQueryNewOrderPriceContext(queryOrderType constant.QueryOrderType) *queryNewOrderPriceContext {
	return &queryNewOrderPriceContext{
		queryOrderType: queryOrderType,
	}
}
