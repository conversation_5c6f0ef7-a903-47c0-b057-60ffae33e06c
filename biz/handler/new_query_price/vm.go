package new_query_price

import (
	"slices"

	"github.com/spf13/cast"
	"work.ctyun.cn/git/openapi/openapi-base/openapi/starship_openapi"
	stmod "work.ctyun.cn/git/openapi/openapi-base/openapi/starship_openapi/model"
	"work.ctyun.cn/git/openapi/openapi-v4-go/biz/constant"
)

type vm struct {
	ctx *queryNewOrderPriceContext
}

func (v *vm) buildItem() (items []map[string]any) {
	items = []map[string]any{
		v.buildVMItem(),
		v.buildSysDiskItem(),
	}
	if v.ctx.req.Bandwidth != nil && *v.ctx.req.Bandwidth != 0 {
		items = append(items, v.buildIpItem())
	}
	if len(v.ctx.req.Disks) > 0 {
		items = append(items, v.buildDiskItems()...)
	}
	return
}

// buildVMItem 云主机
func (v *vm) buildVMItem() map[string]any {
	return map[string]any{
		"resourceType": "VM",
		"serviceTag":   "OVMS",
		"master":       true,
		"itemConfig": map[string]any{
			"platformId":          v.ctx.req.RegionID,
			"regionId":            v.ctx.regionInfo.CtyunId,
			"imageRef":            v.ctx.req.ImageUUID,
			"flavorType":          v.ctx.flavor["flavorType"],
			"cpuNum":              v.ctx.flavor["cpuNum"],
			"memSize":             v.ctx.flavor["memSize"],
			"diskXssdTypeSupport": cast.ToInt(v.ctx.ctySupportSSD), // true: 1, false: 0
		},
		"itemValue": 1,
	}
}

// buildSysDiskItem 系统盘
func (v *vm) buildSysDiskItem() map[string]any {
	return map[string]any{
		"serviceTag":     "OVMS",
		"resourceType":   "EBS",
		"isSystemVolume": true,
		"master":         false,
		"itemValue":      v.ctx.req.SysDiskSize,
		"itemConfig": map[string]any{
			"volumeType": v.ctx.req.SysDiskType,
		},
	}
}

// buildSysDiskItem 构架数据盘item，数据盘可能会有多个item
func (v *vm) buildDiskItems() (result []map[string]any) {
	result = make([]map[string]any, 0, len(v.ctx.req.Disks))
	for _, disk := range v.ctx.req.Disks {
		result = append(result, map[string]any{
			"master":       false,
			"resourceType": "EBS",
			"serviceTag":   "OVMS",
			"itemConfig": map[string]any{
				"volumeType": disk.DiskType,
			},
			"itemValue": disk.DiskSize,
		})
	}
	return
}

// buildIpItem 弹性ip
func (v *vm) buildIpItem() map[string]any {
	return map[string]any{
		"master":       false,
		"resourceType": "NETWORK",
		"serviceTag":   "OVMS",
		"itemConfig": map[string]any{
			"type": "standalone",
		},
		"itemValue": v.ctx.req.Bandwidth,
	}
}

// getVersionByFlavorType GPU_N_PI7 规格类型特殊处理，赋值version，加到询价参数中
func (v *vm) getVersionByFlavorType() (version string) {
	if v.ctx.flavor["flavorType"] != "GPU_N_PI7" {
		return
	}
	// TODO: admin get config GPU_PI7_V2, 后续维护到 apollo 中?
	gpuP17V2 := "1"

	customInfo := v.ctx.ctx.Value(constant.CustomInfo).(map[string]string)
	rsp, e := starship_openapi.GetOpenAPI().GetUserTryKey(v.ctx.ctx, &stmod.GetUserTryKeyRequest{
		RegionId:   v.ctx.req.RegionID,
		CustomerId: customInfo[constant.AccountId],
	})
	if e != nil {
		return
	}
	trialProducts := rsp.ReturnObj
	if gpuP17V2 == "1" || (gpuP17V2 == "2" && slices.Contains(trialProducts, "GPU_PI7_V2")) {
		version = "v2"
	} else {
		version = "v1"
	}

	return
}
